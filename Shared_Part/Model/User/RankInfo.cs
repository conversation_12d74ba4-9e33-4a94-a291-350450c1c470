using System.Collections.Generic;
using MemoryPack;
using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;

namespace MaoYouJi
{
  [EnableClass]
  [MemoryPackable]
  public partial class RankItem
  {
    public long userId; // 用户id
    public string name; // 用户名
    public long attackVal;// 战斗力
    public SkinIdEnum skinId; // 皮肤id
  }

  [EnableClass]
  [MemoryPackable]
  public partial class RankInfo
  {
    [BsonId]
    public long id = 8888; // 固定为8888
    public List<RankItem> items; // 排行榜，按战斗力排序，只取前1000
    public long updateTime; // 更新时间
  }
}