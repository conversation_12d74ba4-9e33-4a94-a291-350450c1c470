using System;

namespace MaoYouJi
{
  public class TimeInfo : Singleton<TimeInfo>, ISingletonAwake
  {
    private int timeZone;

    public int TimeZone
    {
      get
      {
        return this.timeZone;
      }
      set
      {
        this.timeZone = value;
        dt = dt1970.AddHours(TimeZone);
      }
    }

    private DateTime dt1970;
    private DateTime dt;

    // ping消息会设置该值，原子操作
    public long ServerMinusClientTime { private get; set; }

    public long FrameTime { get; private set; }

    public void Awake()
    {
      this.dt1970 = new DateTime(1970, 1, 1, 0, 0, 0, DateTimeKind.Utc);
      this.dt = new DateTime(1970, 1, 1, 0, 0, 0, DateTimeKind.Utc);
      this.FrameTime = this.ClientNow();
      this.TimeZone = 8;
    }

    public void Update()
    {
      // 赋值long型是原子操作，线程安全
      this.FrameTime = this.ClientNow();
    }

    /// <summary> 
    /// 根据时间戳获取时间 
    /// </summary>  
    public DateTime ToDateTime(long timeStamp)
    {
      return dt.AddTicks(timeStamp * 10000);
    }

    public DateTime DelayDateTime(long timeStamp)
    {
      return dt1970.AddTicks((timeStamp + ServerNow()) * 10000);
    }

    // 线程安全
    public long ClientNow()
    {
      return (DateTime.UtcNow.Ticks - this.dt1970.Ticks) / 10000;
    }

    public long ServerNow()
    {
      return ClientNow() + this.ServerMinusClientTime;
    }

    public long ClientFrameTime()
    {
      return this.FrameTime;
    }

    public long ServerFrameTime()
    {
      return this.FrameTime + this.ServerMinusClientTime;
    }

    public long Transition(DateTime d)
    {
      return (d.Ticks - dt.Ticks) / 10000;
    }

    /// <summary>
    /// 将字符串时间（如"06:00"）转为今天对应的毫秒时间戳
    /// </summary>
    public long TodayTimeToTimestamp(string timeStr)
    {
      // 获取当前日期
      DateTime now = DateTime.UtcNow.AddHours(TimeZone);
      DateTime today = new DateTime(now.Year, now.Month, now.Day, 0, 0, 0, DateTimeKind.Utc);

      // 解析字符串时间
      var parts = timeStr.Split(':');
      int hour = int.Parse(parts[0]);
      int minute = int.Parse(parts[1]);

      // 构造今天的目标时间
      DateTime target = today.AddHours(hour).AddMinutes(minute);

      // 转为时间戳
      return Transition(target);
    }

    public static long GetNextTime(List<String> times)
    {
      if (times == null || times.isEmpty())
      {
        return 0; // 或者抛出异常，根据需求决定
      }

      long nowMillis = System.currentTimeMillis();

      List<Long> nextTimestamps = new ArrayList<>();

      SimpleDateFormat sdf = new SimpleDateFormat("HH:mm");
      sdf.setTimeZone(TimeZone.getDefault()); // 使用系统默认时区

      for (String timeStr : times)
      {
        try
        {
          Date parsedTime = sdf.parse(timeStr);
          Calendar targetCal = Calendar.getInstance();
          targetCal.setTime(parsedTime); // 这会保留解析出的小时和分钟，但日期是1970年

          Calendar todayTargetCal = Calendar.getInstance(); // 获取当前的年月日
          todayTargetCal.set(Calendar.HOUR_OF_DAY, targetCal.get(Calendar.HOUR_OF_DAY));
          todayTargetCal.set(Calendar.MINUTE, targetCal.get(Calendar.MINUTE));
          todayTargetCal.set(Calendar.SECOND, 0);
          todayTargetCal.set(Calendar.MILLISECOND, 0);

          long todayMillis = todayTargetCal.getTimeInMillis();

          // 如果今天这个时间点还没过，加入候选列表
          if (todayMillis > (nowMillis + getMinMills(1)))
          {
            nextTimestamps.add(todayMillis);
          }

          // 计算明天这个时间点的时间戳，并加入候选列表
          Calendar tomorrowTargetCal = (Calendar)todayTargetCal.clone();
          tomorrowTargetCal.add(Calendar.DAY_OF_YEAR, 1);
          nextTimestamps.add(tomorrowTargetCal.getTimeInMillis());

        }
        catch (ParseException e)
        {
          // 处理解析错误，例如打印日志或跳过无效格式
          LogUtil.ERROR("Invalid time format: " + timeStr);
          // 可以选择继续处理下一个时间或返回错误码/抛出异常
        }
      }

      if (nextTimestamps.isEmpty())
      {
        // 如果所有时间都在今天之前，并且没有提供任何时间，这里需要处理
        // 这种情况理论上不应该发生，因为我们总是添加了明天的时间戳
        // 但为了健壮性，可以返回0或抛出异常
        return 0;
      }

      // 对所有可能的时间戳排序，找到最小的（即最近的）
      Collections.sort(nextTimestamps);

      // 返回列表中第一个（即最近的）时间戳
      return nextTimestamps.get(0);
    }
  }
}