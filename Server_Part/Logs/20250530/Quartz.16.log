2025-05-30 16:12:05.8109 INFO [Quartz.Impl.StdSchedulerFactory] Default Quartz.NET properties loaded from embedded resource file 
2025-05-30 16:12:05.9980 DEBUG [Quartz.Simpl.TaskSchedulingThreadPool] TaskSchedulingThreadPool configured with max concurrency of 10 and TaskScheduler ThreadPoolTaskScheduler. 
2025-05-30 16:12:06.0003 INFO [Quartz.Core.SchedulerSignalerImpl] Initialized Scheduler Signaller of type: Quartz.Core.SchedulerSignalerImpl 
2025-05-30 16:12:06.0003 INFO [Quartz.Core.QuartzScheduler] Quartz Scheduler created 
2025-05-30 16:12:06.0005 INFO [Quartz.Simpl.RAMJobStore] RAMJobStore initialized. 
2025-05-30 16:12:06.0005 INFO [Quartz.Impl.StdSchedulerFactory] Quartz Scheduler 3.14.0.0 - 'DefaultQuartzScheduler' with instanceId 'NON_CLUSTERED' initialized 
2025-05-30 16:12:06.0005 INFO [Quartz.Impl.StdSchedulerFactory] Using thread pool 'Quartz.Simpl.DefaultThreadPool', size: 10 
2025-05-30 16:12:06.0005 INFO [Quartz.Impl.StdSchedulerFactory] Using job store 'Quartz.Simpl.RAMJobStore', supports persistence: False, clustered: False 
2025-05-30 16:12:06.0028 INFO [Quartz.Core.QuartzScheduler] Scheduler DefaultQuartzScheduler_$_NON_CLUSTERED started. 
2025-05-30 16:12:06.0028 DEBUG [Quartz.Core.QuartzSchedulerThread] Batch acquisition of 0 triggers 
2025-05-30 16:12:06.0835 DEBUG [Quartz.Core.QuartzSchedulerThread] Batch acquisition of 0 triggers 
2025-05-30 16:12:06.0835 DEBUG [Quartz.Core.QuartzSchedulerThread] Batch acquisition of 0 triggers 
2025-05-30 16:12:06.0835 DEBUG [Quartz.Core.QuartzSchedulerThread] Batch acquisition of 0 triggers 
2025-05-30 16:12:06.0835 DEBUG [Quartz.Core.QuartzSchedulerThread] Batch acquisition of 0 triggers 
2025-05-30 16:12:06.0835 DEBUG [Quartz.Core.QuartzSchedulerThread] Batch acquisition of 0 triggers 
2025-05-30 16:12:32.7277 DEBUG [Quartz.Core.QuartzSchedulerThread] Batch acquisition of 0 triggers 
2025-05-30 16:12:56.1338 DEBUG [Quartz.Core.QuartzSchedulerThread] Batch acquisition of 1 triggers 
2025-05-30 16:13:06.0835 DEBUG [Quartz.Simpl.SimpleJobFactory] Producing instance of Job 'global.Global_Biz_Per_1_Min', class=MaoYouJi.MaoScheduleJob 
2025-05-30 16:13:06.0867 DEBUG [Quartz.Core.QuartzSchedulerThread] Batch acquisition of 0 triggers 
2025-05-30 16:13:06.0936 DEBUG [Quartz.Core.JobRunShell] Calling Execute on job global.Global_Biz_Per_1_Min 
2025-05-30 16:13:06.1005 DEBUG [Quartz.Core.JobRunShell] Trigger instruction : NoInstruction 
2025-05-30 16:13:32.9922 DEBUG [Quartz.Core.QuartzSchedulerThread] Batch acquisition of 0 triggers 
2025-05-30 16:14:01.8992 DEBUG [Quartz.Core.QuartzSchedulerThread] Batch acquisition of 1 triggers 
2025-05-30 16:14:06.0880 DEBUG [Quartz.Simpl.SimpleJobFactory] Producing instance of Job 'global.Global_Biz_Per_1_Min', class=MaoYouJi.MaoScheduleJob 
2025-05-30 16:14:06.0919 DEBUG [Quartz.Core.QuartzSchedulerThread] Batch acquisition of 0 triggers 
2025-05-30 16:14:06.0919 DEBUG [Quartz.Core.JobRunShell] Calling Execute on job global.Global_Biz_Per_1_Min 
2025-05-30 16:14:06.0926 DEBUG [Quartz.Core.JobRunShell] Trigger instruction : NoInstruction 
2025-05-30 16:14:29.6507 DEBUG [Quartz.Core.QuartzSchedulerThread] Batch acquisition of 0 triggers 
2025-05-30 16:14:54.7178 DEBUG [Quartz.Core.QuartzSchedulerThread] Batch acquisition of 1 triggers 
2025-05-30 16:15:06.0778 DEBUG [Quartz.Simpl.SimpleJobFactory] Producing instance of Job 'global.Global_Biz_Per_1_Min', class=MaoYouJi.MaoScheduleJob 
2025-05-30 16:15:06.0783 DEBUG [Quartz.Core.QuartzSchedulerThread] Batch acquisition of 0 triggers 
2025-05-30 16:15:06.0783 DEBUG [Quartz.Core.JobRunShell] Calling Execute on job global.Global_Biz_Per_1_Min 
2025-05-30 16:15:06.0783 DEBUG [Quartz.Core.JobRunShell] Trigger instruction : NoInstruction 
