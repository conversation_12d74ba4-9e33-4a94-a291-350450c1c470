using System.Collections.Generic;

namespace MaoYouJi
{
  [MessageHandler(SceneType.Global)]
  public class GetAnnouceReqHandler : MessageHandler<Scene, GetAnnouceReq, GetAnnouceResp>
  {
    protected override async ETTask Run(Scene scene, GetAnnouceReq req, GetAnnouceResp response)
    {
      GlobalManageComp globalManageComp = scene.GetComponent<GlobalManageComp>();
      List<AnnouncementInfo> announcements = await globalManageComp.GetAllAnnouncements();
      response.announcements = announcements;
      await ETTask.CompletedTask;
    }
  }
}