using System.Net;

namespace MaoYouJi
{
  [Invoke((long)SceneType.Global)]
  public class FiberInit_Global : AInvokeHandler<FiberInit, ETTask>
  {
    public override async ETTask Handle(FiberInit fiberInit)
    {
      Scene root = fiberInit.Fiber.Root;
      root.AddComponent<MailBoxComponent, MailBoxType>(MailBoxType.UnOrderedMessage);
      root.AddComponent<TimerComponent>();
      root.AddComponent<CoroutineLockComponent>();
      root.AddComponent<ProcessInnerSender>();
      root.AddComponent<MessageSender>();
      root.AddComponent<DBManagerComponent>();
      root.AddComponent<GlobalManageComp>();

      GlobalActorInfo.Instance.GlobalActorId = root.GetActorId();

      QuartzScheduler.Instance.AddScheduleJob(new MaoScheduleJobInfo(root.GetActorId(), new InnerGlobalTimerPer_1_MinMsg()), JobIdHelper.GLOBAL_GROUP, "Global_Biz_Per_1_Min", 1000 * 60, 1000 * 60, int.MaxValue);
      QuartzScheduler.Instance.AddScheduleJob(new MaoScheduleJobInfo(root.GetActorId(), new InnerGlobalTimerPer_5_MinMsg()), JobIdHelper.GLOBAL_GROUP, "Global_Biz_Per_5_Min", 1000 * 60 * 5, 1000 * 60 * 5, int.MaxValue);
      QuartzScheduler.Instance.AddScheduleJob(new MaoScheduleJobInfo(root.GetActorId(), new InnerGlobalTimerPer_10_MinMsg()), JobIdHelper.GLOBAL_GROUP, "Global_Biz_Per_10_Min", 1000 * 60 * 10, 1000 * 60 * 10, int.MaxValue);
      QuartzScheduler.Instance.AddScheduleJob(new MaoScheduleJobInfo(root.GetActorId(), new InnerGlobalTimerPer_30_MinMsg()), JobIdHelper.GLOBAL_GROUP, "Global_Biz_Per_30_Min", 1000 * 60 * 30, 1000 * 60 * 30, int.MaxValue);
      QuartzScheduler.Instance.AddScheduleJob(new MaoScheduleJobInfo(root.GetActorId(), new InnerGlobalTimerPer_1_HourMsg()), JobIdHelper.GLOBAL_GROUP, "Global_Biz_Per_1_Hour", 1000 * 60 * 60, 1000 * 60 * 60, int.MaxValue);

      await ETTask.CompletedTask;
    }
  }
}